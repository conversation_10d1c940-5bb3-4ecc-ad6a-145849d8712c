declare module '@mui/material/styles' {
  interface Palette {
    customLightBlue: Palette['primary'];
    customLightRed: Palette['primary'];
    customLightOrange: Palette['primary'];
    customLightGreen: Palette['primary'];
    customSignal: Palette['primary'];
  }

  interface PaletteOptions {
    customLightBlue?: PaletteOptions['primary'];
    customLightRed?: PaletteOptions['primary'];
    customLightOrange?: PaletteOptions['primary'];
    customLightGreen?: PaletteOptions['primary'];
    customSignal?: PaletteOptions['primary'];
  }

  interface SimplePaletteColorOptions {
    contrast?: string;
  }

  interface PaletteColor {
    contrast?: string;
  }
}

declare module '@mui/material/Button' {
  interface ButtonPropsColorOverrides {
    customLightBlue: true;
    customLightRed: true;
    customLightOrange: true;
    customLightGreen: true;
    customSignal: true;
  }
}

export const palette = {
  primary: {
    main: '#0044B4', 
    contrastText: '#FFFFFF',
  },
  secondary: {
    main: '#002E7C', 
    contrastText: '#FFFFFF',
  },
  customLightBlue: {
    main: '#1ED2E6',
    contrast: '#00B4D4',
    contrastText: '#000000',
  },
  customLightRed: {
    main: '#FF5A54',
    contrast: '#F05252',
    contrastText: '#FFFFFF',
  },
  customLightOrange: {
    main: '#FF9900',
    contrast: '#EE8700',
    contrastText: '#FFFFFF',
  },
  customLightGreen: {
    main: '#00E191',
    contrast: '#0AC84E',
    contrastText: '#FFFFFF',
  },
  customSignal: {
    main: '#E6FF00',
    contrastText: '#000000',
  },
  background: {
    default: '#F4F6F8',
    paper: '#FFFFFF',
  },
  text: {
    primary: '#1A2027',
    secondary: '#637381',
  },
};