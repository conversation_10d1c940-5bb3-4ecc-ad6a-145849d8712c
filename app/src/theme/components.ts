import type { Components } from '@mui/material/styles';

export const components: Components = {
  MuiButton: {
    defaultProps: {
      disableElevation: true,
    },
    styleOverrides: {
      root: {
        borderRadius: '8px',
        padding: '8px 22px',
      },
      containedPrimary: {
        '&:hover': {
          backgroundColor: '#00338A', 
        },
      },
    },
  },
  MuiAppBar: {
    defaultProps: {
      elevation: 0,
      color: 'primary',
    },
    styleOverrides: {
      root: {
        borderBottom: '1px solid #E0E0E0',
        backgroundColor: '#FFFFFF', 
        color: '#1A2027',
      },
    },
  },
  MuiCard: {
    defaultProps: {
      elevation: 0,
    },
    styleOverrides: {
      root: {
        borderRadius: '12px',
        border: '1px solid #E0E0E0',
      },
    },
  },
};