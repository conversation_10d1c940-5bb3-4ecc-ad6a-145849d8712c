/* Styles for Azure AD authentication components */

.auth-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin: 1rem 0;
}

.auth-button {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.auth-button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.auth-button.login {
  background: linear-gradient(135deg, #0078d4, #106ebe);
  color: white;
  box-shadow: 0 2px 4px rgba(0, 120, 212, 0.3);
}

.auth-button.login:hover:not(:disabled) {
  background: linear-gradient(135deg, #106ebe, #005a9e);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 120, 212, 0.4);
}

.auth-button.logout {
  background: #f3f2f1;
  color: #323130;
  border: 1px solid #d2d0ce;
}

.auth-button.logout:hover:not(:disabled) {
  background: #edebe9;
  border-color: #c8c6c4;
}

.auth-button.loading {
  background: #f3f2f1;
  color: #605e5c;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #0078d4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e1e4e8;
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-weight: 600;
  color: #24292e;
  font-size: 14px;
}

.user-email {
  font-size: 12px;
  color: #586069;
  margin-top: 2px;
}

.auth-error {
  background: #ffeaea;
  color: #d73a49;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #fdaeb7;
  font-size: 14px;
  margin-bottom: 1rem;
}

/* Microsoft branding consistency */
.auth-button.login::before {
  content: "🏢";
  margin-right: 4px;
}

/* Responsive design */
@media (max-width: 768px) {
  .user-info {
    flex-direction: column;
    text-align: center;
  }

  .auth-button {
    width: 100%;
    justify-content: center;
  }
}
