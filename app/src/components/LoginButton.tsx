// Login component for Azure AD authentication
import React from "react";
import { useAuth } from "../auth/useAuth";
import "./LoginButton.css";

interface LoginButtonProps {
  className?: string;
}

export const LoginButton: React.FC<LoginButtonProps> = ({ className }) => {
  const { login, logout, isAuthenticated, user, loading, error } = useAuth();

  const handleAuth = async () => {
    if (isAuthenticated) {
      await logout();
    } else {
      await login();
    }
  };

  if (loading) {
    return (
      <button className={`auth-button loading ${className || ""}`} disabled>
        <span className="spinner"></span>
        Loading...
      </button>
    );
  }

  return (
    <div className="auth-container">
      {error && <div className="auth-error">⚠️ {error}</div>}

      {isAuthenticated && user ? (
        <div className="user-info">
          <div className="user-details">
            <span className="user-name">
              {user.name || user.username || "User"}
            </span>
            <span className="user-email">{user.username}</span>
          </div>
          <button
            className={`auth-button logout ${className || ""}`}
            onClick={handleAuth}
          >
            Sign Out
          </button>
        </div>
      ) : (
        <button
          className={`auth-button login ${className || ""}`}
          onClick={handleAuth}
        >
          🔐 Sign in with Microsoft
        </button>
      )}
    </div>
  );
};
